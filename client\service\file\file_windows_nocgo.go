//go:build windows && nocgo
// +build windows,nocgo

package file

import (
	"os/exec"
	"strconv"
	"strings"
)

// ListFiles will only be called when path is root and
// current system is Windows.
// It will return mount points of all volumes.
func ListFiles(path string) ([]File, error) {
	result := make([]File, 0)
	if len(path) == 0 || path == `\` || path == `/` {
		// Use wmic command to get drive information
		cmd := exec.Command("wmic", "logicaldisk", "get", "size,freespace,caption", "/format:csv")
		output, err := cmd.Output()
		if err != nil {
			// Fallback to simple drive listing
			drives := []string{"C:", "D:", "E:", "F:", "G:", "H:", "I:", "J:", "K:", "L:", "M:", "N:", "O:", "P:", "Q:", "R:", "S:", "T:", "U:", "V:", "W:", "X:", "Y:", "Z:"}
			for _, drive := range drives {
				if _, err := exec.Command("dir", drive+"\\").Output(); err == nil {
					result = append(result, File{Name: drive + "\\", Type: 2, Size: 0})
				}
			}
			return result, nil
		}
		
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			if strings.TrimSpace(line) == "" || strings.Contains(line, "Node") || strings.Contains(line, "Caption") {
				continue
			}
			
			parts := strings.Split(line, ",")
			if len(parts) >= 4 {
				caption := strings.TrimSpace(parts[1])
				sizeStr := strings.TrimSpace(parts[3])
				
				if caption != "" && sizeStr != "" {
					size, err := strconv.ParseUint(sizeStr, 10, 64)
					if err != nil {
						size = 0
					}
					result = append(result, File{Name: caption + "\\", Type: 2, Size: size})
				}
			}
		}
		
		if len(result) == 0 {
			// Final fallback - just add C: drive
			result = append(result, File{Name: "C:\\", Type: 2, Size: 0})
		}
		
		return result, nil
	}
	return listFiles(path)
}
