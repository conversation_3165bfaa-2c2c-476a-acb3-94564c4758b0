//go:build nocgo
// +build nocgo

package core

import (
	"Spark/modules"
	"crypto/rand"
	"encoding/hex"
	"github.com/denisbrodbeck/machineid"
	_net "net"
	"os"
	"os/user"
	"runtime"
	"time"
)

func GetDevice() (*modules.Device, error) {
	device := modules.Device{}
	
	// Basic system info
	device.OS = runtime.GOOS
	device.Arch = runtime.GOARCH
	
	// Hostname
	hostname, err := os.Hostname()
	if err != nil {
		hostname = "Unknown"
	}
	device.Hostname = hostname
	
	// Username
	currentUser, err := user.Current()
	if err != nil {
		device.Username = "Unknown"
	} else {
		device.Username = currentUser.Username
	}
	
	// Machine ID
	id, err := machineid.ID()
	if err != nil {
		// Generate a random ID if machine ID fails
		randomBytes := make([]byte, 16)
		rand.Read(randomBytes)
		id = hex.EncodeToString(randomBytes)
	}
	device.ID = id
	
	// Network interfaces (basic info)
	interfaces, err := _net.Interfaces()
	if err == nil {
		for _, iface := range interfaces {
			if iface.Flags&_net.FlagUp != 0 && iface.Flags&_net.FlagLoopback == 0 {
				addrs, err := iface.Addrs()
				if err == nil && len(addrs) > 0 {
					device.LAN = addrs[0].String()
					break
				}
			}
		}
	}
	
	// Set default values for hardware info (since we can't get them without CGO)
	device.CPU = modules.CPU{
		Model: "Unknown CPU",
		Usage: 0,
	}
	device.RAM = modules.IO{
		Total: 0,
		Used:  0,
		Usage: 0,
	}
	device.Disk = modules.IO{
		Total: 0,
		Used:  0,
		Usage: 0,
	}
	device.Net = modules.Net{
		Sent: 0,
		Recv: 0,
	}

	// Set current time
	device.Uptime = uint64(time.Now().Unix())

	return &device, nil
}

func GetPartialInfo() (*modules.Device, error) {
	device := modules.Device{}

	// Set default values for partial info
	device.CPU = modules.CPU{
		Model: "Unknown CPU",
		Usage: 0,
	}
	device.RAM = modules.IO{
		Total: 0,
		Used:  0,
		Usage: 0,
	}
	device.Disk = modules.IO{
		Total: 0,
		Used:  0,
		Usage: 0,
	}
	device.Net = modules.Net{
		Sent: 0,
		Recv: 0,
	}
	device.Uptime = uint64(time.Now().Unix())

	return &device, nil
}

func GetDeviceID() (string, error) {
	id, err := machineid.ID()
	if err != nil {
		// Generate a random ID if machine ID fails
		randomBytes := make([]byte, 16)
		rand.Read(randomBytes)
		id = hex.EncodeToString(randomBytes)
	}
	return id, nil
}
