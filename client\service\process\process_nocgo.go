//go:build nocgo
// +build nocgo

package process

import (
	"os/exec"
	"runtime"
	"strconv"
	"strings"
)

type Process struct {
	Name string `json:"name"`
	Pid  int32  `json:"pid"`
}

func ListProcesses() ([]Process, error) {
	result := make([]Process, 0)
	
	if runtime.GOOS == "windows" {
		// Use tasklist command on Windows
		cmd := exec.Command("tasklist", "/fo", "csv", "/nh")
		output, err := cmd.Output()
		if err != nil {
			return nil, err
		}
		
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			if strings.TrimSpace(line) == "" {
				continue
			}
			
			// Parse CSV format: "name","pid","session","session#","mem"
			parts := strings.Split(line, ",")
			if len(parts) >= 2 {
				name := strings.Trim(parts[0], "\"")
				pidStr := strings.Trim(parts[1], "\"")
				
				if pid, err := strconv.ParseInt(pidStr, 10, 32); err == nil {
					result = append(result, Process{Name: name, Pid: int32(pid)})
				}
			}
		}
	} else {
		// Use ps command on Unix-like systems
		cmd := exec.Command("ps", "-eo", "pid,comm")
		output, err := cmd.Output()
		if err != nil {
			return nil, err
		}
		
		lines := strings.Split(string(output), "\n")
		for i, line := range lines {
			if i == 0 || strings.TrimSpace(line) == "" {
				continue // Skip header and empty lines
			}
			
			fields := strings.Fields(line)
			if len(fields) >= 2 {
				if pid, err := strconv.ParseInt(fields[0], 10, 32); err == nil {
					name := strings.Join(fields[1:], " ")
					result = append(result, Process{Name: name, Pid: int32(pid)})
				}
			}
		}
	}
	
	return result, nil
}

func KillProcess(pid int32) error {
	if runtime.GOOS == "windows" {
		// Use taskkill command on Windows
		cmd := exec.Command("taskkill", "/F", "/PID", strconv.Itoa(int(pid)))
		return cmd.Run()
	} else {
		// Use kill command on Unix-like systems
		cmd := exec.Command("kill", "-9", strconv.Itoa(int(pid)))
		return cmd.Run()
	}
}
